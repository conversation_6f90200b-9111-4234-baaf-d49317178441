@import 'tailwindcss';

/* <PERSON><PERSON>st Font */
@font-face {
  font-family: 'Geist';
  src: url('/fonts/Geist[wght].woff2') format('woff2-variations');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

:root {
  font-family: 'Geist', system-ui, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: white;
  background-color: #070709;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #070709;
  color: white;
}

#root {
  min-height: 100vh;
}
